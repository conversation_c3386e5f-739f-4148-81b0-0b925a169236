import { 
    updateDoctorAgendaInDataStore, 
    getDoctorAgendaFromDataStore, 
    getDoctorFromDataStore, 
    hasDoctorAgendaInDataStore 
} from '../dataStoreUtils';
import { MedicalCenterEventManager, MedicalCenterEventData } from '@/contexts/MedicalCenterEventsContext';
import { DoctorsForMedicalCenter } from '@/types/doctors/doctorsForMedicalCenter';
import { ProfessionalSchedulesResponse } from '@/types/professional-schedules';

// Mock data
const mockDoctor: DoctorsForMedicalCenter = {
    id: 1,
    name: '<PERSON>',
    surname: '<PERSON><PERSON>',
    specialties: ['Cardiology'],
    medicalLicense: '12345',
    nextAppointment: null,
    nextAppointmentQuantity: null,
    agendaByMonthAndYear: {},
    appointmentIntervalTime: '15',
    healthInsurances: [],
    consultationTypes: [],
    bookingPolicies: {} as any,
    fullName: '<PERSON>',
    appointmentDuration: 15,
    isHealthInsuranceAccepted: () => false,
    isHealthInsuranceIdAccepted: () => false
} as DoctorsForMedicalCenter;

const mockScheduleResponse: ProfessionalSchedulesResponse = {
    yearAndMonthFormat: '2024-01',
    appointments: [],
    appointmentSchedules: [],
    specialSchedules: [],
    blockedSlots: [],
    vacationSchedules: []
} as ProfessionalSchedulesResponse;

describe('dataStoreUtils', () => {
    let mockEventManager: MedicalCenterEventManager;
    let mockDataStore: MedicalCenterEventData;

    beforeEach(() => {
        mockDataStore = {
            patients: [],
            doctors: [mockDoctor]
        };

        mockEventManager = {
            dataStore: mockDataStore,
            updateDataStore: jest.fn((key, data) => {
                mockDataStore[key] = data;
            })
        } as any;
    });

    describe('getDoctorFromDataStore', () => {
        it('should return doctor when found', () => {
            const result = getDoctorFromDataStore(mockDataStore, 1);
            expect(result).toBe(mockDoctor);
        });

        it('should return undefined when doctor not found', () => {
            const result = getDoctorFromDataStore(mockDataStore, 999);
            expect(result).toBeUndefined();
        });

        it('should return undefined when dataStore is undefined', () => {
            const result = getDoctorFromDataStore(undefined, 1);
            expect(result).toBeUndefined();
        });
    });

    describe('hasDoctorAgendaInDataStore', () => {
        it('should return false when agenda does not exist', () => {
            const result = hasDoctorAgendaInDataStore(mockDataStore, 1, '2024-01');
            expect(result).toBe(false);
        });

        it('should return true when agenda exists', () => {
            mockDoctor.agendaByMonthAndYear['2024-01'] = mockScheduleResponse;
            const result = hasDoctorAgendaInDataStore(mockDataStore, 1, '2024-01');
            expect(result).toBe(true);
        });
    });

    describe('getDoctorAgendaFromDataStore', () => {
        it('should return agenda when found', () => {
            mockDoctor.agendaByMonthAndYear['2024-01'] = mockScheduleResponse;
            const result = getDoctorAgendaFromDataStore(mockDataStore, 1, '2024-01');
            expect(result).toBe(mockScheduleResponse);
        });

        it('should return undefined when agenda not found', () => {
            const result = getDoctorAgendaFromDataStore(mockDataStore, 1, '2024-01');
            expect(result).toBeUndefined();
        });
    });

    describe('updateDoctorAgendaInDataStore', () => {
        it('should update doctor agenda in dataStore', () => {
            updateDoctorAgendaInDataStore(mockEventManager, 1, '2024-01', mockScheduleResponse);
            
            expect(mockEventManager.updateDataStore).toHaveBeenCalledWith('doctors', expect.any(Array));
            
            const updatedDoctor = getDoctorFromDataStore(mockDataStore, 1);
            expect(updatedDoctor?.agendaByMonthAndYear['2024-01']).toBe(mockScheduleResponse);
        });

        it('should handle missing eventManager gracefully', () => {
            const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
            updateDoctorAgendaInDataStore(null, 1, '2024-01', mockScheduleResponse);
            expect(consoleSpy).toHaveBeenCalledWith('EventManager or dataStore.doctors not available for update');
            consoleSpy.mockRestore();
        });

        it('should handle missing doctor gracefully', () => {
            const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
            updateDoctorAgendaInDataStore(mockEventManager, 999, '2024-01', mockScheduleResponse);
            expect(consoleSpy).toHaveBeenCalledWith('Doctor with ID 999 not found in dataStore');
            consoleSpy.mockRestore();
        });
    });
});
