import {MedicalCenterEventData, MedicalCenterEventManager} from "@/contexts/MedicalCenterEventsContext";
import {DoctorsForMedicalCenter} from "@/types/doctors/doctorsForMedicalCenter";
import {ProfessionalSchedulesResponse} from "@/types/professional-schedules";

/**
 * Updates a doctor's agenda data in the dataStore
 * @param eventManager - The event manager instance
 * @param doctorId - The ID of the doctor to update
 * @param yearAndMonthFormat - The year-month format key (e.g., "2024-01")
 * @param scheduleResponse - The new schedule data
 */
export function updateDoctorAgendaInDataStore(
    eventManager: MedicalCenterEventManager | null,
    doctorId: number,
    yearAndMonthFormat: string,
    scheduleResponse: ProfessionalSchedulesResponse
): void {
    if (!eventManager || !eventManager.dataStore?.doctors) {
        console.warn('EventManager or dataStore.doctors not available for update');
        return;
    }

    const doctorIndex = eventManager.dataStore.doctors.findIndex(d => d.id === doctorId);
    if (doctorIndex === -1) {
        console.warn(`Doctor with ID ${doctorId} not found in dataStore`);
        return;
    }

    const updatedDoctors: DoctorsForMedicalCenter[] = [...eventManager.dataStore.doctors];
    const doctorToUpdate: DoctorsForMedicalCenter = updatedDoctors[doctorIndex];

    doctorToUpdate.agendaByMonthAndYear = {
        ...doctorToUpdate.agendaByMonthAndYear,
        [yearAndMonthFormat]: scheduleResponse
    };

    updatedDoctors[doctorIndex] = doctorToUpdate;

    eventManager.updateDataStore('doctors', updatedDoctors);

    console.log(`Updated doctor ${doctorId} agenda for ${yearAndMonthFormat} in dataStore`);
}

/**
 * Gets a doctor's agenda data from the dataStore
 * @param dataStore - The dataStore instance
 * @param doctorId - The ID of the doctor
 * @param yearAndMonthFormat - The year-month format key (e.g., "2024-01")
 * @returns The schedule response or undefined if not found
 */
export function getDoctorAgendaFromDataStore(
    dataStore: MedicalCenterEventData | undefined,
    doctorId: number,
    yearAndMonthFormat: string
): ProfessionalSchedulesResponse | undefined {
    if (!dataStore?.doctors) {
        return undefined;
    }

    const doctor = dataStore.doctors.find(d => d.id === doctorId);
    return doctor?.agendaByMonthAndYear[yearAndMonthFormat];
}

/**
 * Gets a doctor from the dataStore
 * @param dataStore - The dataStore instance
 * @param doctorId - The ID of the doctor
 * @returns The doctor or undefined if not found
 */
export function getDoctorFromDataStore(
    dataStore: MedicalCenterEventData | undefined,
    doctorId: number
): DoctorsForMedicalCenter | undefined {
    if (!dataStore?.doctors) {
        return undefined;
    }

    return dataStore.doctors.find(d => d.id === doctorId);
}

/**
 * Checks if a doctor's agenda exists in the dataStore for a specific month/year
 * @param dataStore - The dataStore instance
 * @param doctorId - The ID of the doctor
 * @param yearAndMonthFormat - The year-month format key (e.g., "2024-01")
 * @returns True if the agenda exists, false otherwise
 */
export function hasDoctorAgendaInDataStore(
    dataStore: MedicalCenterEventData | undefined,
    doctorId: number,
    yearAndMonthFormat: string
): boolean {
    const agenda = getDoctorAgendaFromDataStore(dataStore, doctorId, yearAndMonthFormat);
    return agenda !== undefined;
}
